'use client';

import PropTypes from 'prop-types';
import React, { useMemo } from 'react';

import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Alert from '@mui/material/Alert';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import CardHeader from '@mui/material/CardHeader';
import CardContent from '@mui/material/CardContent';

import { FORM_FIELDS, PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

import { Iconify } from 'src/components/iconify';

import useVariantManager from './hooks/useVariantManager';
import useAttributeManager from './hooks/useAttributeManager';
import {
  VariantTable,
  BulkEditMenu,
  AttributeForm,
  AttributeList,
  ImageSelector,
  EditAttributeDialog,
} from './components';

// ----------------------------------------------------------------------

/**
 * Chuyển đổi attributes từ object format sang array format
 * @param {Object|Array} attributes - Thuộc tính ở dạng object hoặc array
 * @returns {Array} - Thuộc tính ở dạng array
 */
function normalizeAttributes(attributes) {
  // Nếu đã là array, trả về nguyên vẹn
  if (Array.isArray(attributes)) {
    return attributes;
  }

  // Nếu là object, chuyển đổi sang array format
  if (attributes && typeof attributes === 'object') {
    return Object.entries(attributes).map(([name, values]) => ({
      name,
      values: Array.isArray(values) ? values : [],
    }));
  }

  // Trường hợp khác, trả về array rỗng
  return [];
}

export default function ProductVariants({ watch, setValue, isEditMode = false, canEditAttributes = true }) {
  const productType = watch(FORM_FIELDS.PRODUCT_TYPE);

  // Tối ưu: Sử dụng useMemo để tránh tạo mảng mới mỗi lần render và normalize attributes
  const attributes = useMemo(() => {
    const rawAttributes = watch('attributes');
    return normalizeAttributes(rawAttributes);
  }, [watch]);

  // Tối ưu: Sử dụng custom hooks để quản lý biến thể và thuộc tính với edit mode support
  const variantManager = useVariantManager(watch, setValue, isEditMode);
  const attributeManager = useAttributeManager(watch, setValue, variantManager.generateVariants, isEditMode, canEditAttributes);

  // Chỉ hiển thị khi là sản phẩm có biến thể
  if (productType !== PRODUCT_TYPES.VARIABLE) {
    return null;
  }

  return (
    <Card>
      <CardHeader title="Biến thể sản phẩm" />

      {/* Menu cập nhật hàng loạt */}
      <BulkEditMenu
        anchorEl={variantManager.anchorEl}
        onClose={variantManager.handleBulkMenuClose}
        bulkEditField={variantManager.bulkEditField}
        setBulkEditField={variantManager.setBulkEditField}
        bulkEditValue={variantManager.bulkEditValue}
        setBulkEditValue={variantManager.setBulkEditValue}
        onBulkEdit={variantManager.handleBulkEdit}
        isEditMode={isEditMode}
      />

      <CardContent>
        <Stack spacing={3}>
          {/* Tối ưu: Thông báo chế độ edit với thông tin chi tiết */}
          {isEditMode && (
            <Alert severity="info" variant="outlined">
              <strong>Chế độ chỉnh sửa:</strong>
              {canEditAttributes ? (
                <>Bạn chỉ có thể thêm giá trị mới cho các thuộc tính đã có. Không thể thay đổi tên thuộc tính hoặc xóa thuộc tính.</>
              ) : (
                <>Bạn chỉ có thể cập nhật thông tin các biến thể hiện có. Không thể thêm/sửa/xóa thuộc tính hoặc chỉnh sửa tồn kho biến thể.</>
              )}
            </Alert>
          )}

          {/* Tối ưu: Form thêm thuộc tính - Ẩn trong chế độ edit khi không cho phép */}
          {(!isEditMode || canEditAttributes) && (
            <AttributeForm
              attributeName={attributeManager.attributeName}
              setAttributeName={attributeManager.setAttributeName}
              attributeValue={attributeManager.attributeValue}
              setAttributeValue={attributeManager.setAttributeValue}
              attributeValues={attributeManager.attributeValues}
              handleAddAttributeValue={attributeManager.handleAddAttributeValue}
              handleRemoveAttributeValue={attributeManager.handleRemoveAttributeValue}
              handleAddAttribute={attributeManager.handleAddAttribute}
              isEditMode={isEditMode}
              canEditAttributes={canEditAttributes}
              existingAttributes={attributes}
            />
          )}

          {/* Danh sách thuộc tính đã thêm */}
          <>
            <Divider />
            <AttributeList
              attributes={attributes}
              handleEditAttribute={attributeManager.handleEditAttribute}
              handleRemoveAttribute={attributeManager.handleRemoveAttribute}
              isEditMode={isEditMode}
              canEditAttributes={canEditAttributes}
            />
          </>

          {/* Danh sách biến thể */}
          {(() => {
            const formVariants = watch('variants') || [];

            return formVariants.length > 0 ? (
              <>
                <Button
                  variant="outlined"
                  startIcon={<Iconify icon="eva:edit-fill" />}
                  onClick={variantManager.handleBulkMenuOpen}
                  disabled={isEditMode}
                >
                  Cập nhật hàng loạt
                </Button>
                <Divider />
                <VariantTable
                  variants={formVariants}
                  handleVariantChange={variantManager.handleVariantChange}
                  handleOpenImageSelect={variantManager.handleOpenImageSelect}
                  handleRemoveVariantImage={variantManager.handleRemoveVariantImage}
                  isEditMode={isEditMode}
                />
              </>
            ) : null;
          })()}
        </Stack>
      </CardContent>

      {/* Dialog chỉnh sửa thuộc tính - Ẩn trong chế độ edit */}
      {!isEditMode && (
        <EditAttributeDialog
          open={attributeManager.editDialogOpen}
          onClose={() => attributeManager.setEditDialogOpen(false)}
          attributeName={attributeManager.attributeName}
          setAttributeName={attributeManager.setAttributeName}
          attributeValue={attributeManager.attributeValue}
          setAttributeValue={attributeManager.setAttributeValue}
          attributeValues={attributeManager.attributeValues}
          handleAddAttributeValue={attributeManager.handleAddAttributeValue}
          handleRemoveAttributeValue={attributeManager.handleRemoveAttributeValue}
          handleSaveEditAttribute={attributeManager.handleSaveEditAttribute}
        />
      )}

      {/* Dialog chọn ảnh cho biến thể */}
      <ImageSelector
        open={variantManager.imageSelectOpen}
        media={watch('images') || []}
        onClose={() => variantManager.setImageSelectOpen(false)}
        onSelectImage={variantManager.handleSelectVariantImage}
        selectedImage={variantManager.getSelectedVariantImage()}
      />
    </Card>
  );
}

ProductVariants.propTypes = {
  watch: PropTypes.func,
  setValue: PropTypes.func,
  isEditMode: PropTypes.bool,
};
