'use client';

import { fetchData, updateData, upsertData, deleteData } from './supabase-utils';
import {
  MESSAGE_TYPES,
  convertLegacyRule,
  createFormattedFollowupRule,
  validateMessageFormatting
} from './message-formatting-service';

import {
  processImagesForSaving,
  MESSAGE_IMAGE_CONFIG
} from './message-image-service';

const TABLE_NAME = 'channel_automation_followup';

export async function getChannelAutomationConfig(channelId) {
  if (!channelId) {
    return { success: false, error: 'Channel ID is required', data: null };
  }

  try {
    const result = await fetchData(TABLE_NAME, {
      filters: { channelId },
      single: true
    });

    if (!result.success && (result.error?.code === 'NO_ROWS_OR_MULTIPLE_ROWS' || result.error?.code === 'PGRST116')) {
      return { success: true, data: null, error: null };
    }

    return result;
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * <PERSON><PERSON>y danh sách cấu hình automation follow-up với các tùy chọn lọc
 * @param {Object} options - <PERSON><PERSON><PERSON> tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getChannelAutomationConfigs(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Tạo hoặc cập nhật cấu hình automation follow-up cho channel
 * @param {string} channelId - ID của channel
 * @param {Object} configData - Dữ liệu cấu hình
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertChannelAutomationConfig(channelId, configData) {
  if (!channelId) {
    return { success: false, error: 'Channel ID is required', data: null };
  }

  try {
    // Get existing config to compare images
    const existingConfig = await getChannelAutomationConfig(channelId);
    const existingRules = existingConfig.success && existingConfig.data ?
      existingConfig.data.followupRules || [] : [];

    // Process images in followup rules
    const processedRules = await processFollowupRulesImages(configData.followupRules || []);

    if (!processedRules.success) {
      return {
        success: false,
        error: processedRules.error,
        data: null
      };
    }

    // Cleanup old images that are no longer used
    await cleanupUnusedImages(existingRules, processedRules.data);

    const data = {
      channelId,
      ...configData,
      followupRules: processedRules.data
    };

    return upsertData(TABLE_NAME, data, ['tenantId', 'channelId']);

  } catch (error) {
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi lưu cấu hình' },
      data: null
    };
  }
}

/**
 * Cập nhật cấu hình automation follow-up
 * @param {string} configId - ID của cấu hình
 * @param {Object} configData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateChannelAutomationConfig(configId, configData) {
  if (!configId) {
    return { success: false, error: 'Config ID is required', data: null };
  }

  return updateData(TABLE_NAME, configData, { id: configId });
}

/**
 * Xóa cấu hình automation follow-up
 * @param {string} configId - ID của cấu hình
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteChannelAutomationConfig(configId) {
  if (!configId) {
    return { success: false, error: 'Config ID is required', data: null };
  }

  return deleteData(TABLE_NAME, { id: configId });
}

/**
 * Bật/tắt automation follow-up cho channel
 * @param {string} channelId - ID của channel
 * @param {boolean} isEnabled - Trạng thái bật/tắt
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function toggleChannelAutomation(channelId, isEnabled) {
  if (!channelId) {
    return { success: false, error: 'Channel ID is required', data: null };
  }

  try {
    // Kiểm tra xem đã có cấu hình chưa
    const existingConfig = await getChannelAutomationConfig(channelId);

    if (existingConfig.success && existingConfig.data) {
      // Cập nhật cấu hình hiện có
      return updateChannelAutomationConfig(existingConfig.data.id, { isEnabled });
    } else if (existingConfig.success && !existingConfig.data) {
      // Chưa có cấu hình - tạo mới
      return upsertChannelAutomationConfig(channelId, {
        isEnabled,
        followupRules: []
      });
    } else {
      // Có lỗi khi kiểm tra
      return existingConfig;
    }
  } catch (error) {
    return { success: false, error, data: null };
  }
}

/**
 * Utility functions để xử lý followup rules
 */

/**
 * Tạo rule mới với ID duy nhất (backward compatibility)
 * @param {number} delayMinutes - Thời gian delay (phút)
 * @param {string} message - Nội dung tin nhắn
 * @returns {Object} - Rule object (order sẽ được tự động tính toán khi sort)
 */
export function createFollowupRule(delayMinutes, message) {
  return {
    id: `rule_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    delayMinutes: parseInt(delayMinutes, 10),
    message: message.trim(),
    messageType: MESSAGE_TYPES.TEXT,
    messageFormatting: null,
    isEnabled: true,
    order: 1, // Temporary order, sẽ được cập nhật khi sort
    createdAt: new Date().toISOString()
  };
}

/**
 * Tạo rule mới với hỗ trợ message formatting
 * @param {number} delayMinutes - Thời gian delay (phút)
 * @param {string|Object} messageData - Nội dung tin nhắn hoặc message formatting object
 * @returns {Object} - Rule object với message formatting
 */
export function createFollowupRuleWithFormatting(delayMinutes, messageData) {
  return createFormattedFollowupRule(delayMinutes, messageData);
}

/**
 * Validate followup rule với hỗ trợ message formatting
 * @param {Object} rule - Rule object
 * @returns {Object} - Validation result
 */
export function validateFollowupRule(rule) {
  const errors = [];

  if (!rule.delayMinutes || rule.delayMinutes < 1) {
    errors.push('Thời gian delay phải lớn hơn 0 phút');
  }

  if (rule.delayMinutes > 43200) { // 30 days in minutes
    errors.push('Thời gian delay không được vượt quá 30 ngày');
  }

  if (!rule.message || rule.message.trim().length === 0) {
    errors.push('Nội dung tin nhắn không được để trống');
  }

  if (rule.message && rule.message.length > 2000) {
    errors.push('Nội dung tin nhắn không được vượt quá 2000 ký tự');
  }

  // Validate message formatting nếu có
  if (rule.messageType === MESSAGE_TYPES.FORMATTED && rule.messageFormatting) {
    const messageData = {
      content: rule.message,
      images: rule.messageFormatting.images || [],
      buttons: rule.messageFormatting.buttons || []
    };

    const formattingValidation = validateMessageFormatting(messageData);
    if (!formattingValidation.isValid) {
      errors.push(...formattingValidation.errors);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sắp xếp rules theo thứ tự thời gian và cập nhật order với backward compatibility
 * @param {Array} rules - Mảng rules
 * @returns {Array} - Mảng rules đã sắp xếp
 */
export function sortFollowupRules(rules) {
  if (!Array.isArray(rules) || rules.length === 0) {
    return [];
  }

  // Convert legacy rules để đảm bảo backward compatibility
  const convertedRules = rules.map(rule => convertLegacyRule(rule));

  // Sắp xếp theo delayMinutes tăng dần
  const sortedRules = [...convertedRules].sort((a, b) => a.delayMinutes - b.delayMinutes);

  // Cập nhật order theo thứ tự mới
  return sortedRules.map((rule, index) => ({
    ...rule,
    order: index + 1
  }));
}

/**
 * Kiểm tra trùng thời gian delay
 * @param {Array} existingRules - Mảng rules hiện tại
 * @param {number} newDelayMinutes - Thời gian delay mới
 * @param {string} excludeRuleId - ID rule cần loại trừ (khi edit)
 * @returns {boolean} - true nếu bị trùng
 */
export function isDuplicateDelayTime(existingRules, newDelayMinutes, excludeRuleId = null) {
  return existingRules.some(rule =>
    rule.id !== excludeRuleId && rule.delayMinutes === newDelayMinutes
  );
}

/**
 * Chuyển đổi từ value và unit thành tổng số phút
 * @param {number} value - Giá trị số
 * @param {string} unit - Đơn vị ('minutes', 'hours', 'days')
 * @returns {number} - Tổng số phút
 */
export function convertToMinutes(value, unit) {
  const timeUnit = TIME_UNITS.find(u => u.value === unit);
  if (!timeUnit) return value; // fallback
  return value * timeUnit.multiplier;
}

/**
 * Chuyển đổi từ tổng số phút thành value và unit tối ưu
 * @param {number} minutes - Tổng số phút
 * @returns {Object} - { value, unit }
 */
export function convertFromMinutes(minutes) {
  // Ưu tiên ngày nếu chia hết
  if (minutes >= 1440 && minutes % 1440 === 0) {
    return { value: minutes / 1440, unit: 'days' };
  }
  // Ưu tiên giờ nếu chia hết
  if (minutes >= 60 && minutes % 60 === 0) {
    return { value: minutes / 60, unit: 'hours' };
  }
  // Mặc định là phút
  return { value: minutes, unit: 'minutes' };
}

/**
 * Format thời gian delay thành text dễ đọc
 * @param {number} minutes - Số phút
 * @returns {string} - Text format
 */
export function formatDelayTime(minutes) {
  const { value, unit } = convertFromMinutes(minutes);
  const timeUnit = TIME_UNITS.find(u => u.value === unit);
  return `${value} ${timeUnit?.label || 'phút'}`;
}

/**
 * Validate giá trị thời gian
 * @param {number} value - Giá trị số
 * @param {string} unit - Đơn vị
 * @returns {Object} - { isValid, error, minutes }
 */
export function validateTimeValue(value, unit) {
  if (!value || value <= 0) {
    return { isValid: false, error: 'Giá trị phải lớn hơn 0', minutes: 0 };
  }

  const minutes = convertToMinutes(value, unit);

  if (minutes > 43200) { // 30 days
    return { isValid: false, error: 'Thời gian không được vượt quá 30 ngày', minutes };
  }

  return { isValid: true, error: null, minutes };
}

/**
 * Đơn vị thời gian
 */
export const TIME_UNITS = [
  { value: 'minutes', label: 'Phút', multiplier: 1 },
  { value: 'hours', label: 'Giờ', multiplier: 60 },
  { value: 'days', label: 'Ngày', multiplier: 1440 }
];

/**
 * Preset thời gian delay phổ biến theo đơn vị
 */
export const DELAY_PRESETS = {
  minutes: [
    { value: 5, label: '5 phút' },
    { value: 10, label: '10 phút' },
    { value: 15, label: '15 phút' },
    { value: 30, label: '30 phút' },
    { value: 45, label: '45 phút' }
  ],
  hours: [
    { value: 1, label: '1 giờ' },
    { value: 2, label: '2 giờ' },
    { value: 3, label: '3 giờ' },
    { value: 5, label: '5 giờ' },
    { value: 8, label: '8 giờ' },
    { value: 12, label: '12 giờ' }
  ],
  days: [
    { value: 1, label: '1 ngày' },
    { value: 2, label: '2 ngày' },
    { value: 3, label: '3 ngày' },
    { value: 7, label: '1 tuần' },
    { value: 14, label: '2 tuần' },
    { value: 30, label: '1 tháng' }
  ]
};

/**
 * Template tin nhắn mẫu
 */
export const MESSAGE_TEMPLATES = [
  'Xin chào! Bạn có cần hỗ trợ gì thêm không?',
  'Chúng tôi vẫn sẵn sàng hỗ trợ bạn. Hãy liên hệ nếu cần!',
  'Bạn có muốn tìm hiểu thêm về sản phẩm/dịch vụ của chúng tôi?',
  'Cảm ơn bạn đã quan tâm! Chúng tôi có thể giúp gì cho bạn?',
  'Bạn có câu hỏi nào khác mà chúng tôi có thể hỗ trợ không?'
];

// =====================================================
// IMAGE PROCESSING FUNCTIONS
// =====================================================

/**
 * Process images in followup rules - move temporary to permanent
 * @param {Array} rules - Array of followup rules
 * @returns {Promise<Object>} - Processing result
 */
async function processFollowupRulesImages(rules) {
  try {
    if (!rules || rules.length === 0) {
      return { success: true, data: [], error: null };
    }

    console.log('🔄 Processing images for followup rules...');

    const processedRules = await Promise.all(
      rules.map(async (rule) => {
        if (rule.messageType === MESSAGE_TYPES.FORMATTED && rule.messageFormatting?.images) {
          console.log(`📸 Processing images for rule ${rule.id}:`, rule.messageFormatting.images.length);

          // Process images for this rule - move temporary to permanent
          const imageResult = await processImagesForSaving(rule.messageFormatting.images);

          if (!imageResult.success) {
            console.error('❌ Failed to process images for rule:', rule.id, imageResult.error);
            // Continue with original images if processing fails
            return rule;
          }

          console.log(`✅ Successfully processed ${imageResult.data.length} images for rule ${rule.id}`);

          return {
            ...rule,
            messageFormatting: {
              ...rule.messageFormatting,
              images: imageResult.data // Now this is array of URLs only
            }
          };
        }

        return rule;
      })
    );

    console.log('✅ All followup rules images processed successfully');
    return { success: true, data: processedRules, error: null };

  } catch (error) {
    console.error('Error processing followup rules images:', error);
    return {
      success: false,
      error: { message: error.message || 'Lỗi không xác định khi xử lý hình ảnh' },
      data: null
    };
  }
}

/**
 * Cleanup unused images from old rules
 * @param {Array} oldRules - Previous rules
 * @param {Array} newRules - New rules
 * @returns {Promise<void>}
 */
async function cleanupUnusedImages(oldRules, newRules) {
  try {
    if (!oldRules || oldRules.length === 0) {
      return;
    }

    // Collect all images from old and new rules
    const oldImages = [];
    const newImages = [];

    oldRules.forEach(rule => {
      if (rule.messageType === MESSAGE_TYPES.FORMATTED && rule.messageFormatting?.images) {
        oldImages.push(...rule.messageFormatting.images);
      }
    });

    newRules.forEach(rule => {
      if (rule.messageType === MESSAGE_TYPES.FORMATTED && rule.messageFormatting?.images) {
        newImages.push(...rule.messageFormatting.images);
      }
    });

    // Find images to cleanup - now working with URLs
    const imagesToCleanup = oldImages.filter(oldUrl => !newImages.includes(oldUrl));

    if (imagesToCleanup.length > 0) {
      console.log('🗑️ Cleaning up unused images:', imagesToCleanup.length);

      // Use deleteFilesWithPublicUrl like product-service
      try {
        const { storageService } = await import('./storage-service');
        const cleanupResult = await storageService.deleteFilesWithPublicUrl(
          MESSAGE_IMAGE_CONFIG.BUCKET,
          imagesToCleanup
        );

        if (cleanupResult.success) {
          console.log('✅ Successfully cleaned up unused images');
        } else {
          console.error('⚠️ Some images failed to cleanup:', cleanupResult.error);
        }
      } catch (error) {
        console.error('❌ Error cleaning up unused images:', error);
      }
    } else {
      console.log('✅ No unused images to cleanup');
    }

  } catch (error) {
    console.error('Error cleaning up unused images:', error);
    // Don't throw error, just log it
  }
}
