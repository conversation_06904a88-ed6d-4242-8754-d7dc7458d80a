'use client';

import PropTypes from 'prop-types';
import { useState, useRef, useCallback } from 'react';

import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import CircularProgress from '@mui/material/CircularProgress';
import { alpha, useTheme } from '@mui/material/styles';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import {
  MESSAGE_IMAGE_CONFIG,
  uploadMessageImagesTemporary,
  validateImageFiles,
  extractFilePathFromUrl,
  deleteMessageImage,
  cleanupTemporaryImages
} from 'src/actions/mooly-chatbot/message-image-service';

// ----------------------------------------------------------------------

export default function CompactImageUpload({ 
  images = [], 
  onChange, 
  disabled = false,
  maxImages = MESSAGE_IMAGE_CONFIG.MAX_IMAGES 
}) {
  const theme = useTheme();
  const fileInputRef = useRef(null);
  const [uploading, setUploading] = useState(false);

  const handleFileSelect = useCallback(async (event) => {
    const files = Array.from(event.target.files || []);

    if (!files.length) return;

    if (images.length + files.length > maxImages) {
      toast.error(`Tối đa ${maxImages} hình ảnh`);
      return;
    }

    const validation = validateImageFiles(files);
    if (!validation.isValid) {
      toast.error(validation.errors.join(', '));
      return;
    }

    setUploading(true);

    try {
      const uploadResult = await uploadMessageImagesTemporary(files);

      if (uploadResult.success) {
        const newImages = [...images, ...uploadResult.data];
        onChange?.(newImages);
        toast.success(`Upload thành công ${uploadResult.data.length} hình ảnh`);
      } else {
        toast.error(uploadResult.error?.message || 'Upload thất bại');
      }
    } catch (error) {
      toast.error('Có lỗi xảy ra khi upload hình ảnh');
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [images, maxImages, onChange]);

  // Handle remove image
  const handleRemoveImage = useCallback(async (imageToRemove, index) => {
    try {
      // Handle both URL strings and image objects
      if (typeof imageToRemove === 'object' && imageToRemove.isTemporary && imageToRemove.filePath) {
        // Only delete from storage if it's temporary (permanent ones will be handled on save)
        await deleteMessageImage(imageToRemove.filePath);
      }

      // Remove from local state - handle both formats
      const updatedImages = images.filter((img, idx) => {
        if (typeof img === 'string' && typeof imageToRemove === 'string') {
          return img !== imageToRemove;
        }
        if (typeof img === 'object' && typeof imageToRemove === 'object') {
          return img.id !== imageToRemove.id;
        }
        return idx !== index; // Fallback to index-based removal
      });

      onChange?.(updatedImages);
      toast.success('Đã xóa hình ảnh');
    } catch (error) {
      console.error('Error removing image:', error);
      toast.error('Có lỗi xảy ra khi xóa hình ảnh');
    }
  }, [images, onChange]);

  // Handle click upload button
  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const canUploadMore = images.length < maxImages;

  return (
    <Box>
      {/* Header with upload button */}
      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
        <Typography variant="body2" sx={{ fontWeight: 500 }}>
          Hình ảnh ({images.length}/{maxImages})
        </Typography>
        
        {canUploadMore && (
          <Button
            size="small"
            variant="outlined"
            startIcon={uploading ? <CircularProgress size={16} /> : <Iconify icon="eva:plus-fill" />}
            onClick={handleUploadClick}
            disabled={disabled || uploading}
            sx={{ 
              minWidth: 'auto',
              px: 1.5,
              py: 0.5,
              fontSize: '0.75rem'
            }}
          >
            {uploading ? 'Đang tải...' : 'Thêm hình'}
          </Button>
        )}
      </Stack>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={MESSAGE_IMAGE_CONFIG.ALLOWED_TYPES.join(',')}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        disabled={disabled || uploading}
      />

      {/* Images grid */}
      {images.length > 0 ? (
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {images.map((image, index) => {
            // Handle both URL strings and image objects
            const imageUrl = typeof image === 'string' ? image : image.url;
            const imageId = typeof image === 'string' ? `url_${index}` : image.id;
            const imageAlt = typeof image === 'string' ? 'Image' : (image.alt || 'Uploaded image');

            return (
              <Box
                key={imageId}
                sx={{
                  position: 'relative',
                  width: 60,
                  height: 60,
                  borderRadius: 1,
                  overflow: 'hidden',
                  border: `1px solid ${alpha(theme.palette.grey[500], 0.3)}`,
                  '&:hover .remove-button': {
                    opacity: 1
                  }
                }}
              >
                <img
                  src={imageUrl}
                  alt={imageAlt}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover'
                  }}
                />

                {/* Remove button */}
                <IconButton
                  className="remove-button"
                  size="small"
                  sx={{
                    position: 'absolute',
                    top: 2,
                    right: 2,
                    bgcolor: 'error.main',
                    color: 'white',
                    opacity: 0,
                    transition: 'opacity 0.2s',
                    '&:hover': {
                      bgcolor: 'error.dark',
                      opacity: 1
                    },
                    width: 20,
                    height: 20,
                    minWidth: 20
                  }}
                  onClick={() => handleRemoveImage(image, index)}
                  disabled={disabled}
                >
                  <Iconify icon="eva:close-fill" width={12} />
                </IconButton>
              </Box>
          )})}
        </Stack>
      ) : (
        <Box
          sx={{
            p: 2,
            border: `1px dashed ${alpha(theme.palette.grey[500], 0.3)}`,
            borderRadius: 1,
            textAlign: 'center',
            bgcolor: alpha(theme.palette.grey[500], 0.04)
          }}
        >
          <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '0.875rem' }}>
            Chưa có hình ảnh nào
          </Typography>
        </Box>
      )}

      {/* Upload info */}
      {canUploadMore && (
        <Stack direction="row" spacing={1} mt={1} flexWrap="wrap" useFlexGap>
          <Chip
            size="small"
            label={`Tối đa ${maxImages} hình`}
            variant="outlined"
            sx={{ fontSize: '0.75rem', height: 20 }}
          />
          <Chip
            size="small"
            label="JPG, PNG, GIF, WebP"
            variant="outlined"
            sx={{ fontSize: '0.75rem', height: 20 }}
          />
          <Chip
            size="small"
            label="Max 5MB/file"
            variant="outlined"
            sx={{ fontSize: '0.75rem', height: 20 }}
          />
        </Stack>
      )}
    </Box>
  );
}

CompactImageUpload.propTypes = {
  images: PropTypes.array,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  maxImages: PropTypes.number,
};
