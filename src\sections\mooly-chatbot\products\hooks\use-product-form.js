'use client';

import { useMemo, useState, useEffect, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';
import { checkSKUExists } from 'src/actions/mooly-chatbot/product-api';

import { getSchemaByType, getDefaultValuesByType, defaultValues } from '../product-schema';

/**
 * Custom hook để quản lý form sản phẩm với validation tối ưu
 * @param {Object} options - Tùy chọn cấu hình
 * @param {Object} options.currentProduct - Sản phẩm hiện tại (cho chế độ edit)
 * @param {string} options.productId - ID sản phẩm (cho chế độ edit)
 * @param {boolean} options.isEditMode - Chế độ edit hay tạo mới
 * @returns {Object} - Form methods và utilities
 */
export function useProductForm({ currentProduct = null, productId = null, isEditMode = false, initialProductType = 'simple' } = {}) {
  // Xử lý dữ liệu currentProduct an toàn
  const safeCurrentProduct = useMemo(() => {
    if (!currentProduct) return null;

    const normalizeAttributes = (attributes) => {
      // Nếu đã là array, trả về nguyên vẹn
      if (Array.isArray(attributes)) {
        return attributes;
      }

      // Nếu là object, chuyển đổi sang array format
      if (attributes && typeof attributes === 'object') {
        // Chuyển đổi từ {key: [values]} sang [{name: key, values: [values]}]
        return Object.entries(attributes).map(([name, values]) => ({
          name,
          values: Array.isArray(values) ? values : [],
        }));
      }

      // Trường hợp khác, trả về array rỗng
      return [];
    };

    const safeProduct = {
      ...currentProduct,
      tags: Array.isArray(currentProduct.tags) ? currentProduct.tags : [],
      gender: Array.isArray(currentProduct.gender) ? currentProduct.gender : [],
      images: Array.isArray(currentProduct.images) ? currentProduct.images : [],
      variants: Array.isArray(currentProduct.variants) ? currentProduct.variants : [],
      saleLabel: currentProduct.saleLabel || { enabled: false, content: '' },
      newLabel: currentProduct.newLabel || { enabled: false, content: '' },
      attributes: normalizeAttributes(currentProduct.attributes),
      // Đảm bảo các field SEO có giá trị mặc định để tránh validation error
      seoTitle: currentProduct.seoTitle || '',
      seoDescription: currentProduct.seoDescription || '',
      metaKeywords: Array.isArray(currentProduct.metaKeywords) ? currentProduct.metaKeywords : [],
      // Đảm bảo các field text có giá trị string
      description: currentProduct.description || '',
      url: currentProduct.url || '',
      slug: currentProduct.slug || '',
      sku: currentProduct.sku || '',
      // Các field khác có thể null/undefined
      costPrice: currentProduct.costPrice || null,
      salePrice: currentProduct.salePrice || null,
      weight: currentProduct.weight || null,
      length: currentProduct.length || null,
      width: currentProduct.width || null,
      height: currentProduct.height || null,
      taxes: currentProduct.taxes || null,
      includeTaxes: currentProduct.includeTaxes || false,
      isFeatured: currentProduct.isFeatured || false,
      trackInventory: currentProduct.trackInventory || false,
      stockQuantity: currentProduct.stockQuantity || 0,
    };

    return safeProduct;
  }, [currentProduct]);

  // State cho loại sản phẩm hiện tại
  const [currentProductType, setCurrentProductType] = useState(
    safeCurrentProduct?.type || initialProductType || defaultValues.type
  );

  // Tạo schema dựa trên loại sản phẩm - QUAN TRỌNG cho validation đồng bộ
  const currentSchema = useMemo(() => getSchemaByType(currentProductType), [currentProductType]);

  // Lấy default values phù hợp với loại sản phẩm
  const currentDefaultValues = useMemo(() => {
    const baseDefaults = getDefaultValuesByType(currentProductType);
    return {
      ...baseDefaults,
      type: currentProductType,
    };
  }, [currentProductType]);

  // Khởi tạo form với React Hook Form
  const methods = useForm({
    mode: isEditMode ? 'onChange' : 'onBlur', // Edit mode: validate onChange, Create mode: validate onBlur
    resolver: zodResolver(currentSchema),
    defaultValues: isEditMode ? safeCurrentProduct : currentDefaultValues,
    // Sử dụng values để force update form khi dữ liệu thay đổi
    values: isEditMode ? safeCurrentProduct : undefined,
  });

  const {
    watch,
    setValue,
    getValues,
    handleSubmit,
    reset,
    formState: { isSubmitting, errors, isValid },
  } = methods;

  // Watch product type để cập nhật schema và reset form khi cần
  const productType = watch('type');

  useEffect(() => {
    if (productType && productType !== currentProductType) {
      setCurrentProductType(productType);

      // Reset form với default values mới khi thay đổi loại sản phẩm
      // Chỉ reset khi không phải edit mode để tránh mất dữ liệu
      if (!isEditMode) {
        const newDefaultValues = getDefaultValuesByType(productType);
        const currentValues = getValues();

        // Giữ lại các giá trị đã nhập (name, description, categoryId, images, url)
        const preservedValues = {
          name: currentValues.name || '',
          description: currentValues.description || '',
          categoryId: currentValues.categoryId || '',
          images: currentValues.images || [],
          avatar: currentValues.avatar || null,
          url: currentValues.url || '',
        };

        // Reset với default values mới nhưng giữ lại các giá trị quan trọng
        reset({
          ...newDefaultValues,
          ...preservedValues,
          type: productType,
        });
      }
    }
  }, [productType, currentProductType, isEditMode, reset, getValues]);

  // Validate SKU - cho phép SKU trống để tự động tạo
  const validateSKU = useCallback(async (sku) => {
    // Nếu SKU trống, không cần validate (sẽ tự động tạo)
    if (!sku || sku.trim() === '') {
      return; // Cho phép SKU trống
    }

    try {
      const exists = await checkSKUExists(sku, isEditMode ? productId : null);
      if (exists) {
        throw new Error('SKU đã tồn tại. Vui lòng sử dụng SKU khác.');
      }
    } catch (checkError) {
      console.error('Error checking SKU:', checkError);
      throw new Error('Không thể kiểm tra SKU. Vui lòng thử lại.');
    }
  }, [isEditMode, productId]);

  // Chuẩn bị dữ liệu trước khi submit - tối ưu theo loại sản phẩm
  const prepareSubmitData = useCallback((data) => {
    const productData = { ...data };

    // Đảm bảo dữ liệu đúng định dạng chung
    productData.price = Number(data.price) || 0;
    productData.stockQuantity = Number(data.stockQuantity) || 0;
    productData.images = data.images || [];
    productData.isActive = data.isActive !== false;
    productData.trackInventory = data.trackInventory !== false;

    // Xử lý dữ liệu theo loại sản phẩm
    if (productType === PRODUCT_TYPES.SIMPLE) {
      // Sản phẩm đơn giản - loại bỏ các trường không cần thiết
      productData.attributes = [];
      productData.variants = [];
      productData.tags = data.tags || [];
      productData.gender = [];
      productData.saleLabel = { enabled: false, content: '' };
      productData.newLabel = { enabled: false, content: '' };
      productData.metaKeywords = [];
      productData.isFeatured = false;
      productData.taxes = null;
      productData.includeTaxes = false;
      productData.costPrice = null;
      productData.salePrice = null;
    } else if (productType === PRODUCT_TYPES.VARIABLE) {
      // Sản phẩm có biến thể - đảm bảo có attributes và variants
      productData.attributes = data.attributes || [];
      productData.variants = data.variants || [];

      // Validate variants có đầy đủ thông tin
      if (productData.variants.length > 0) {
        productData.variants = productData.variants.map(variant => ({
          ...variant,
          price: Number(variant.price) || productData.price,
          stockQuantity: Number(variant.stockQuantity) || 0,
          isActive: variant.isActive !== false,
        }));
      }
    }

    return productData;
  }, [productType]);

  // Validation bổ sung cho variable product
  const validateVariableProduct = useCallback((data) => {
    if (data.type === PRODUCT_TYPES.VARIABLE) {
      // Kiểm tra attributes
      if (!data.attributes || data.attributes.length === 0) {
        throw new Error('Sản phẩm có biến thể phải có ít nhất một thuộc tính');
      }

      // Kiểm tra mỗi attribute có values
      for (const attr of data.attributes) {
        if (!attr.values || attr.values.length === 0) {
          throw new Error(`Thuộc tính "${attr.name}" phải có ít nhất một giá trị`);
        }
      }

      // Kiểm tra variants nếu có
      if (data.variants && data.variants.length > 0) {
        for (const variant of data.variants) {
          if (!variant.price || variant.price < 0) {
            throw new Error('Tất cả biến thể phải có giá hợp lệ');
          }
        }
      }
    }
  }, []);

  // Submit handler với validation tự động và đồng bộ theo loại sản phẩm
  const createSubmitHandler = useCallback((onSubmitSuccess, onSubmitError) => handleSubmit(async (data) => {
      try {
        // Validate SKU trước khi submit
        await validateSKU(data.sku);

        // Validation bổ sung cho variable product
        validateVariableProduct(data);

        // Chuẩn bị dữ liệu
        const productData = prepareSubmitData(data);

        // Gọi callback success
        await onSubmitSuccess(productData);
      } catch (error) {
        // Gọi callback error
        onSubmitError(error);
      }
    }), [handleSubmit, validateSKU, validateVariableProduct, prepareSubmitData]);

  // Utilities
  const isSimpleProduct = productType === PRODUCT_TYPES.SIMPLE;
  const hasErrors = Object.keys(errors).length > 0;

  // Auto-generate slug từ name
  const generateSlug = useCallback((name) => {
    if (!name) return '';
    
    return name
      .toLowerCase()
      .trim()
      .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, 'a')
      .replace(/[èéẹẻẽêềếệểễ]/g, 'e')
      .replace(/[ìíịỉĩ]/g, 'i')
      .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, 'o')
      .replace(/[ùúụủũưừứựửữ]/g, 'u')
      .replace(/[ỳýỵỷỹ]/g, 'y')
      .replace(/đ/g, 'd')
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }, []);

  // Auto-generate slug khi name thay đổi
  const productName = watch('name');
  useEffect(() => {
    if (productName && !isEditMode) {
      const slug = generateSlug(productName);
      setValue('slug', slug);
    }
  }, [productName, isEditMode, generateSlug, setValue]);

  return {
    // Form methods
    ...methods,
    
    // Custom handlers
    createSubmitHandler,
    
    // Utilities
    isSimpleProduct,
    hasErrors,
    isValid,
    currentProductType,
    
    // States
    isSubmitting,
    errors,
    
    // Helpers
    generateSlug,
    prepareSubmitData,
    validateSKU,
  };
}
