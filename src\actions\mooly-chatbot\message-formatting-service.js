'use client';

/**
 * Optimized Message Formatting Service
 * Tối ưu hóa quản lý định dạng tin nhắn với hình ảnh và nút bấm
 */

// =====================================================
// CONSTANTS
// =====================================================

export const MESSAGE_TYPES = {
  TEXT: 'text',
  FORMATTED: 'formatted'
};

export const BUTTON_TYPES = {
  LINK: 'link',
  POSTBACK: 'postback'
};

export const FORMATTING_LIMITS = {
  MAX_IMAGES: 3,
  MAX_BUTTONS: 3,
  MAX_BUTTON_TITLE_LENGTH: 50,
  MAX_MESSAGE_LENGTH: 2000
};

// =====================================================
// VALIDATION FUNCTIONS
// =====================================================

export function validateMessageFormatting(messageData) {
  const errors = [];

  if (!messageData.content?.trim()) {
    errors.push('Nội dung tin nhắn không được để trống');
  }

  if (messageData.content?.length > FORMATTING_LIMITS.MAX_MESSAGE_LENGTH) {
    errors.push(`Nội dung vượt quá ${FORMATTING_LIMITS.MAX_MESSAGE_LENGTH} ký tự`);
  }

  if (messageData.images?.length > FORMATTING_LIMITS.MAX_IMAGES) {
    errors.push(`Tối đa ${FORMATTING_LIMITS.MAX_IMAGES} hình ảnh`);
  }

  if (messageData.buttons?.length > FORMATTING_LIMITS.MAX_BUTTONS) {
    errors.push(`Tối đa ${FORMATTING_LIMITS.MAX_BUTTONS} nút bấm`);
  }

  messageData.buttons?.forEach((button, index) => {
    if (!button.title?.trim()) {
      errors.push(`Tiêu đề nút ${index + 1} không được để trống`);
    }
    if (button.title?.length > FORMATTING_LIMITS.MAX_BUTTON_TITLE_LENGTH) {
      errors.push(`Tiêu đề nút ${index + 1} quá dài`);
    }
    if (button.type === BUTTON_TYPES.LINK && !isValidUrl(button.url)) {
      errors.push(`URL nút ${index + 1} không hợp lệ`);
    }
    if (button.type === BUTTON_TYPES.POSTBACK && !button.payload?.trim()) {
      errors.push(`Payload nút ${index + 1} không được để trống`);
    }
  });

  return { isValid: errors.length === 0, errors };
}

function isValidUrl(url) {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// =====================================================
// MESSAGE FORMATTING FUNCTIONS
// =====================================================

export function createMessageFormatting(content = '', images = [], buttons = []) {
  return {
    type: MESSAGE_TYPES.FORMATTED,
    content: content.trim(),
    images: images.slice(0, FORMATTING_LIMITS.MAX_IMAGES),
    buttons: buttons.slice(0, FORMATTING_LIMITS.MAX_BUTTONS),
    createdAt: new Date().toISOString()
  };
}

export function createButton(title, type, value) {
  const button = {
    id: `btn_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    title: title.trim(),
    type
  };

  if (type === BUTTON_TYPES.LINK) {
    button.url = value;
  } else if (type === BUTTON_TYPES.POSTBACK) {
    button.payload = value;
  }

  return button;
}

export function isValidImageObject(imageObj) {
  return imageObj &&
         typeof imageObj === 'object' &&
         imageObj.id &&
         imageObj.url &&
         typeof imageObj.url === 'string' &&
         imageObj.url.startsWith('http');
}

// =====================================================
// FOLLOWUP RULE EXTENSIONS
// =====================================================

export function createFormattedFollowupRule(delayMinutes, messageData) {
  const rule = {
    id: `rule_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    delayMinutes: parseInt(delayMinutes, 10),
    isEnabled: true,
    order: 1,
    createdAt: new Date().toISOString()
  };

  if (typeof messageData === 'string') {
    rule.message = messageData.trim();
    rule.messageType = MESSAGE_TYPES.TEXT;
  } else if (messageData && typeof messageData === 'object') {
    rule.message = messageData.content || '';
    rule.messageType = MESSAGE_TYPES.FORMATTED;
    rule.messageFormatting = {
      images: messageData.images || [],
      buttons: messageData.buttons || []
    };
  }

  return rule;
}

export function convertLegacyRule(rule) {
  if (!rule.messageType) {
    return {
      ...rule,
      messageType: MESSAGE_TYPES.TEXT,
      messageFormatting: null
    };
  }
  return rule;
}

export function hasMessageFormatting(rule) {
  return rule.messageType === MESSAGE_TYPES.FORMATTED &&
         rule.messageFormatting &&
         (rule.messageFormatting.images?.length > 0 || rule.messageFormatting.buttons?.length > 0);
}

// =====================================================
// PREVIEW FUNCTIONS
// =====================================================

export function generateMessagePreview(rule) {
  let preview = rule.message || '';

  if (hasMessageFormatting(rule)) {
    const { images, buttons } = rule.messageFormatting;

    if (images?.length > 0) {
      preview += ` [${images.length} hình ảnh]`;
    }

    if (buttons?.length > 0) {
      preview += ` [${buttons.length} nút bấm]`;
    }
  }

  return preview.length > 100 ? `${preview.substring(0, 100)}...` : preview;
}

// =====================================================
// EXPORT CONSTANTS
// =====================================================

export const BUTTON_TYPE_OPTIONS = [
  { value: BUTTON_TYPES.LINK, label: 'Liên kết (Link)', icon: 'eva:external-link-fill' },
  { value: BUTTON_TYPES.POSTBACK, label: 'Hành động (Postback)', icon: 'eva:flash-fill' }
];

export const MESSAGE_TEMPLATES_FORMATTED = [
  {
    content: 'Xin chào! Bạn có cần hỗ trợ gì thêm không?',
    messageType: MESSAGE_TYPES.FORMATTED,
    messageFormatting: {
      images: [],
      buttons: [
        { id: 'btn_1', title: 'Tư vấn sản phẩm', type: BUTTON_TYPES.POSTBACK, payload: 'PRODUCT_CONSULTATION' },
        { id: 'btn_2', title: 'Liên hệ hotline', type: BUTTON_TYPES.LINK, url: 'tel:+84123456789' }
      ]
    }
  },
  {
    content: 'Cảm ơn bạn đã quan tâm! Chúng tôi có thể giúp gì cho bạn?',
    messageType: MESSAGE_TYPES.FORMATTED,
    messageFormatting: {
      images: [],
      buttons: [
        { id: 'btn_3', title: 'Xem sản phẩm', type: BUTTON_TYPES.POSTBACK, payload: 'VIEW_PRODUCTS' },
        { id: 'btn_4', title: 'Báo giá', type: BUTTON_TYPES.POSTBACK, payload: 'REQUEST_QUOTE' },
        { id: 'btn_5', title: 'Website', type: BUTTON_TYPES.LINK, url: 'https://example.com' }
      ]
    }
  }
];
