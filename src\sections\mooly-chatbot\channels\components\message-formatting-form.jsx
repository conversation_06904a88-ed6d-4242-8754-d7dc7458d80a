'use client';

import { z as zod } from 'zod';
import PropTypes from 'prop-types';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useState, useCallback, useEffect, useRef, useMemo } from 'react';

import Box from '@mui/material/Box';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import { alpha, useTheme } from '@mui/material/styles';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Form, Field } from 'src/components/hook-form';

import CompactImageUpload from './compact-image-upload';

import {
  MESSAGE_TYPES,
  BUTTON_TYPES,
  FORMATTING_LIMITS,
  BUTTON_TYPE_OPTIONS,
  createButton
} from 'src/actions/mooly-chatbot/message-formatting-service';

// ----------------------------------------------------------------------

// Schema validation - Simplified for formatted messages only
const MessageFormattingSchema = zod.object({
  message: zod.string().min(1, { message: 'Nội dung tin nhắn không được để trống' }),
  messageFormatting: zod.object({
    images: zod.array(zod.string()).optional(),
    buttons: zod.array(zod.object({
      id: zod.string(),
      title: zod.string().min(1, { message: 'Tiêu đề nút không được để trống' }),
      type: zod.enum([BUTTON_TYPES.LINK, BUTTON_TYPES.POSTBACK]),
      url: zod.string().optional(),
      payload: zod.string().optional()
    })).optional()
  }).optional()
});

// Button form schema - Simplified
const ButtonFormSchema = zod.object({
  title: zod.string().min(1, { message: 'Tiêu đề nút không được để trống' }),
  type: zod.enum([BUTTON_TYPES.LINK, BUTTON_TYPES.POSTBACK]),
  value: zod.string().min(1, { message: 'Giá trị không được để trống' })
});

export default function MessageFormattingForm({
  value,
  onChange,
  disabled = false
}) {
  const theme = useTheme();

  // Helper functions
  const convertUrlsToImageObjects = useCallback((imageData) => {
    if (!imageData?.length) return [];
    return imageData.map((item, index) => {
      if (typeof item === 'string') {
        return {
          id: `loaded_${index}_${Date.now()}`,
          url: item,
          alt: 'Loaded image',
          isTemporary: false
        };
      }
      return item;
    });
  }, []);

  const convertImageObjectsToUrls = useCallback((imageData) => {
    if (!imageData?.length) return [];
    return imageData.map(item => typeof item === 'string' ? item : item.url);
  }, []);

  // Default values - Always formatted message type
  const defaultValues = {
    message: value?.message || '',
    messageFormatting: {
      images: value?.messageFormatting?.images || [],
      buttons: value?.messageFormatting?.buttons || []
    }
  };

  // Main form
  const methods = useForm({
    resolver: zodResolver(MessageFormattingSchema),
    defaultValues,
    values: value ? {
      message: value.message || '',
      messageFormatting: {
        images: value.messageFormatting?.images || [],
        buttons: value.messageFormatting?.buttons || []
      }
    } : undefined
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = methods;
  const watchedValues = watch();

  // Button form state
  const [showButtonForm, setShowButtonForm] = useState(false);
  const [editingButton, setEditingButton] = useState(null);

  // Button form
  const buttonMethods = useForm({
    resolver: zodResolver(ButtonFormSchema),
    defaultValues: {
      title: '',
      type: BUTTON_TYPES.LINK,
      value: ''
    }
  });

  const {
    handleSubmit: handleButtonSubmit,
    reset: resetButtonForm,
    setValue: setButtonValue,
    watch: watchButton,
    formState: { errors: buttonErrors }
  } = buttonMethods;

  // Use ref to track if we should update parent
  const isInitializedRef = useRef(false);
  const timeoutRef = useRef(null);

  // Debounced update parent function
  const debouncedUpdateParent = useCallback((data) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      onChange?.(data);
    }, 100); // 100ms debounce
  }, [onChange]);

  // Update parent when form values change
  useEffect(() => {
    if (!isInitializedRef.current) {
      isInitializedRef.current = true;
      return; // Skip first render
    }

    const messageData = {
      message: watchedValues.message || '',
      messageType: MESSAGE_TYPES.FORMATTED, // Always formatted
      messageFormatting: {
        images: watchedValues.messageFormatting?.images || [],
        buttons: watchedValues.messageFormatting?.buttons || []
      }
    };

    debouncedUpdateParent(messageData);

    // Cleanup timeout on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [watchedValues.message, watchedValues.messageFormatting, debouncedUpdateParent]);

  const handleImageChange = useCallback((newImages) => {
    const imageUrls = convertImageObjectsToUrls(newImages);
    setValue('messageFormatting.images', imageUrls);
  }, [setValue, convertImageObjectsToUrls]);

  const resetButtonFormHandler = () => {
    resetButtonForm();
    setEditingButton(null);
    setShowButtonForm(false);
  };

  const handleAddButton = () => {
    const currentButtons = watchedValues.messageFormatting?.buttons || [];
    if (currentButtons.length >= FORMATTING_LIMITS.MAX_BUTTONS) {
      return; // Silent limit - no toast notification
    }
    resetButtonFormHandler();
    setShowButtonForm(true);
  };

  const handleEditButton = (button) => {
    setButtonValue('title', button.title);
    setButtonValue('type', button.type);
    setButtonValue('value', button.url || button.payload || '');
    setEditingButton(button);
    setShowButtonForm(true);
  };

  const onSubmitButton = handleButtonSubmit((data) => {
    const currentButtons = watchedValues.messageFormatting?.buttons || [];

    if (editingButton) {
      const updatedButtons = currentButtons.map(btn =>
        btn.id === editingButton.id
          ? createButton(data.title, data.type, data.value)
          : btn
      );
      setValue('messageFormatting.buttons', updatedButtons);
    } else {
      const newButton = createButton(data.title, data.type, data.value);
      setValue('messageFormatting.buttons', [...currentButtons, newButton]);
    }

    resetButtonFormHandler();
  });

  const handleDeleteButton = (buttonId) => {
    const currentButtons = watchedValues.messageFormatting?.buttons || [];
    const updatedButtons = currentButtons.filter(btn => btn.id !== buttonId);
    setValue('messageFormatting.buttons', updatedButtons);
  };

  return (
    <Stack spacing={2}>
      {/* Message Content */}
      <Form methods={methods} onSubmit={() => {}}>
        <Field.Text
          name="message"
          multiline
          rows={3}
          label="Nội dung tin nhắn"
          placeholder="Nhập nội dung tin nhắn..."
          disabled={disabled}
          size="small"
        />
      </Form>

      {/* Images Section */}
      <CompactImageUpload
        images={convertUrlsToImageObjects(watchedValues.messageFormatting?.images || [])}
        onChange={handleImageChange}
        disabled={disabled}
        maxImages={FORMATTING_LIMITS.MAX_IMAGES}
      />

      {/* Buttons Section */}
      <Box>
        <Stack direction="row" alignItems="center" justifyContent="space-between" mb={1}>
          <Typography variant="body2" sx={{ fontWeight: 500 }}>
            Nút bấm ({(watchedValues.messageFormatting?.buttons || []).length}/{FORMATTING_LIMITS.MAX_BUTTONS})
          </Typography>
          <Button
            size="small"
            variant="outlined"
            startIcon={<Iconify icon="eva:plus-fill" />}
            onClick={handleAddButton}
            disabled={disabled || (watchedValues.messageFormatting?.buttons || []).length >= FORMATTING_LIMITS.MAX_BUTTONS}
          >
            Thêm nút
          </Button>
        </Stack>

            {/* Buttons List */}
            {(watchedValues.messageFormatting?.buttons || []).length > 0 && (
              <Stack spacing={1} mb={1}>
                {(watchedValues.messageFormatting?.buttons || []).map((button) => (
                  <Card
                    key={button.id}
                    sx={{
                      p: 1.5,
                      border: `1px solid ${alpha(theme.palette.grey[500], 0.3)}`
                    }}
                  >
                    <Stack direction="row" alignItems="center" spacing={2}>
                      <Iconify
                        icon={BUTTON_TYPE_OPTIONS.find(opt => opt.value === button.type)?.icon || 'eva:link-fill'}
                        width={16}
                      />
                      <Box flexGrow={1}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {button.title}
                        </Typography>
                        <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                          {button.type === BUTTON_TYPES.LINK ? button.url : button.payload}
                        </Typography>
                      </Box>
                      <Stack direction="row" spacing={0.5}>
                        <IconButton
                          size="small"
                          onClick={() => handleEditButton(button)}
                          disabled={disabled}
                        >
                          <Iconify icon="eva:edit-fill" width={16} />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteButton(button.id)}
                          disabled={disabled}
                        >
                          <Iconify icon="eva:trash-2-fill" width={16} />
                        </IconButton>
                      </Stack>
                    </Stack>
                  </Card>
                ))}
              </Stack>
            )}

        {/* Button Form */}
        {showButtonForm && (
          <Card sx={{ p: 2, border: `2px dashed ${theme.palette.primary.main}` }}>
            <Typography variant="subtitle2" mb={2}>
              {editingButton ? 'Chỉnh sửa nút' : 'Thêm nút mới'}
            </Typography>

            <Form methods={buttonMethods} onSubmit={onSubmitButton}>
              <Stack spacing={2}>
                <Field.Text
                  name="title"
                  label="Tiêu đề nút"
                  size="small"
                />

                <Field.Select
                  name="type"
                  label="Loại nút"
                  size="small"
                >
                  {BUTTON_TYPE_OPTIONS.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      <Stack direction="row" alignItems="center" spacing={1}>
                        <Iconify icon={option.icon} width={16} />
                        <span>{option.label}</span>
                      </Stack>
                    </MenuItem>
                  ))}
                </Field.Select>

                <Field.Text
                  name="value"
                  label={watchButton('type') === BUTTON_TYPES.LINK ? 'URL' : 'Payload'}
                  placeholder={watchButton('type') === BUTTON_TYPES.LINK ? 'https://example.com' : 'ACTION_NAME'}
                  size="small"
                />

                <Stack direction="row" spacing={1} justifyContent="flex-end">
                  <Button size="small" onClick={resetButtonFormHandler}>
                    Hủy
                  </Button>
                  <Button
                    size="small"
                    variant="contained"
                    type="submit"
                  >
                    {editingButton ? 'Cập nhật' : 'Thêm'}
                  </Button>
                </Stack>
              </Stack>
            </Form>
          </Card>
        )}
      </Box>
    </Stack>
  );
}

MessageFormattingForm.propTypes = {
  value: PropTypes.object,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
};
